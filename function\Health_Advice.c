#include "driver/main.h"

// ???嶨???????????У????????????

// ========== ????????????????????????? ==========
// ????BMI???????????????豩???
static int get_bmi_category(float height, float weight)
{
    float bmi = weight / (height * height);
    if (bmi < 18.5)
        return 0; // ???
    else if (bmi < 24)
        return 1; // ????
    else if (bmi < 28)
        return 2; // ????
    else
        return 3; // ????
}

// ?????????????????????豩???
static int get_bp_category(float systolic, float diastolic)
{
    if (systolic < 120 && diastolic < 80)
        return 0; // ????
    else if (systolic < 130 && diastolic < 85)
        return 1; // ???????
    else if (systolic < 140 && diastolic < 90)
        return 2; // ??????
    else
        return 3; // ????
}

// ????eGFR????????С????????????????
static float calculate_egfr(int age, int gender, float creatinine)
{
    float kappa = (gender == 1) ? 0.9 : 0.7;
    float alpha = (gender == 1) ? -0.302 : -0.241;
    float min_cr = fmin(creatinine / kappa, 1.0);
    float max_cr = fmax(creatinine / kappa, 1.0);

    float egfr = 141 * pow(min_cr, alpha) * pow(max_cr, -1.209) * pow(0.993, age);
    if (gender == 0)
        egfr *= 1.018; // ???У??
    return egfr;
}

// ========== ??????????ж????? ==========
HealthAdvice generate_health_advice(HealthData data)
{
    HealthAdvice advice = {0};
    float bmi = data.weight / (data.height * data.height);
    float egfr = calculate_egfr(data.age, data.gender, data.creatinine);

    // 1. ??????飺?á????嶯???????????
    if (get_bmi_category(data.height, data.weight) >= 2)
    { // ????/????
        sprintf(advice.weight,
                "?????е?????????????1?????/?????????????????????粤?????????????????3?ο?????????裬???30????????");
    }
    else if (get_bmi_category(data.height, data.weight) == 0)
    { // ???
        sprintf(advice.weight,
                "?????????????????????????????????????????????10??????????????????????????");
    }
    else
    { // ????
        sprintf(advice.weight, "???????ò????????????????????????");
    }

    // 2. ?????飺?á???????????????
    int bp_cat = get_bp_category(data.systolic_bp, data.diastolic_bp);
    if (bp_cat >= 2)
    { // ???/????
        sprintf(advice.blood_pressure,
                "??????????????α???????????????????????κ?????????????????????????????θ??????Ъ??????????5?????30??????????????");
    }
    else
    { // ????
        sprintf(advice.blood_pressure, "?????????????????????????????????嵭?????");
    }

    // 3. ?????飺?á??滻????????
    if (data.fasting_glucose >= 6.09 || data.hba1c >= 5.69)
    { // ?????
        sprintf(advice.blood_sugar,
                "????е?С????????????????????????????/???????????????????????Щ???????????????????????????");
    }
    else
    { // ????
        sprintf(advice.blood_sugar, "??????????????????????????????????????");
    }

    // 4. ?????飺?á?????????????
    if (data.cholesterol >= 5.19 || data.ldl >= 3.39)
    { // ????
        sprintf(advice.blood_lipid,
                "?????????????????????????????????????????Щ?к??????????????4????????????????г?????????");
    }
    else
    { // ????
        sprintf(advice.blood_lipid, "??????ú????????????Σ???????????????");
    }

    // 5. ????????飺?á????????????
    if (egfr < 90)
    { // ?????????
        sprintf(advice.kidney,
                "???????????????????????????????????????????????????????????????1500-2000???????????");
    }
    else
    { // ????
        sprintf(advice.kidney, "??????????????????????????????????");
    }

    // 6. ??????飺?á?????????????????
    if (data.is_smoker)
    {
        sprintf(advice.smoking,
                "????????岻???????????????1????????????????????С??????????????????????????");
    }
    else
    {
        sprintf(advice.smoking, "?????????????????飬????????????");
    }

    // 7. ????????飺?á???????????????
    if (data.family_history)
    {
        sprintf(advice.family,
                "?????????????????????????????????????????????????飬?緢???????");
    }
    else
    {
        sprintf(advice.family, "??????????????????????????δ???");
    }

    // 8. ??????飺?á???????????????
    if (data.hs_crp >= 1.99)
    {
        sprintf(advice.inflammation,
                "?????е????????????????????Щ???????????????????????7С?");
    }
    else
    {
        sprintf(advice.inflammation, "?????????????????????????????");
    }

    // 9. ?????a???飺?á????+???????????
    if (data.lp_a > 50)
    {
        sprintf(advice.lp_a,
                "?????a???????????????費??????????????????????????????");
    }
    else
    {
        sprintf(advice.lp_a, "?????a??????????????????飬?????????????");
    }

    // 10. ?????飺??????????
    sprintf(advice.summary,
            "???????%s\n"
            "??????%s\n"
            "??????%s\n"
            "??????%s\n"
            "?????????%s\n"
            "???????%s\n"
            "?????????%s\n"
            "???????%s\n"
            "???????a??%s\n"
            "???3?????????????????仯",
            advice.weight,
            advice.blood_pressure,
            advice.blood_sugar,
            advice.blood_lipid,
            advice.kidney,
            advice.smoking,
            advice.family,
            advice.inflammation,
            advice.lp_a);

    return advice;
}

// ========== ????????????????? ==========
