#include "driver/main.h"

// 行动建议结构体（分维度存储）
typedef struct
{
    char weight[256];         // 体重建议
    char blood_pressure[256]; // 血压建议
    char blood_sugar[256];    // 血糖建议
    char blood_lipid[256];    // 血脂建议
    char kidney[256];         // 肾功能建议
    char smoking[256];        // 吸烟建议
    char family[256];         // 家族史建议
    char inflammation[256];   // 炎症建议
    char lp_a[256];           // 脂蛋白a建议
    char summary[1024];       // 综合建议
} HealthAdvice;

// ========== 辅助函数（复用之前的医学逻辑） ==========
// 计算BMI分类（内部逻辑，无需暴露）
static int get_bmi_category(float height, float weight)
{
    float bmi = weight / (height * height);
    if (bmi < 18.5)
        return 0; // 偏瘦
    else if (bmi < 24)
        return 1; // 正常
    else if (bmi < 28)
        return 2; // 超重
    else
        return 3; // 肥胖
}

// 计算血压分类（内部逻辑，无需暴露）
static int get_bp_category(float systolic, float diastolic)
{
    if (systolic < 120 && diastolic < 80)
        return 0; // 正常
    else if (systolic < 130 && diastolic < 85)
        return 1; // 正常高值
    else if (systolic < 140 && diastolic < 90)
        return 2; // 临界高血压
    else
        return 3; // 高血压
}

// 计算eGFR（估算肾小球滤过率，内部逻辑）
static float calculate_egfr(int age, int gender, float creatinine)
{
    float kappa = (gender == 1) ? 0.9 : 0.7;
    float alpha = (gender == 1) ? -0.302 : -0.241;
    float min_cr = fmin(creatinine / kappa, 1.0);
    float max_cr = fmax(creatinine / kappa, 1.0);

    float egfr = 141 * pow(min_cr, alpha) * pow(max_cr, -1.209) * pow(0.993, age);
    if (gender == 0)
        egfr *= 1.018; // 女性校正
    return egfr;
}

// ========== 核心：生成行动建议 ==========
HealthAdvice generate_health_advice(HealthData data)
{
    HealthAdvice advice = {0};
    float bmi = data.weight / (data.height * data.height);
    float egfr = calculate_egfr(data.age, data.gender, data.creatinine);

    // 1. 体重建议：用「具体动作」降低门槛
    if (get_bmi_category(data.height, data.weight) >= 2)
    { // 超重/肥胖
        sprintf(advice.weight,
                "体重有点超重啦，每天少吃1个馒头/半碗米饭，多吃绿叶菜（比如菠菜、生菜），每周至少3次快走或跳广场舞，每次30分钟以上");
    }
    else if (get_bmi_category(data.height, data.weight) == 0)
    { // 偏瘦
        sprintf(advice.weight,
                "体重偏轻哦，每天加个鸡蛋或一杯牛奶，试试吃点坚果（比如每天10颗腰果），别熬夜，让体重稳一点");
    }
    else
    { // 正常
        sprintf(advice.weight, "体重保持得不错，继续吃好睡好，偶尔动一动");
    }

    // 2. 血压建议：用「生活化场景」引导
    int bp_cat = get_bp_category(data.systolic_bp, data.diastolic_bp);
    if (bp_cat >= 2)
    { // 临界/高血压
        sprintf(advice.blood_pressure,
                "血压要注意啦！每天盐别超过一个啤酒瓶盖的量，吃饭少放盐和酱油，每天早晚测血压记下来，要是头晕赶紧坐下歇着，每周至少5天、每次30分钟的散步或太极拳");
    }
    else
    { // 正常
        sprintf(advice.blood_pressure, "血压挺稳的，少熬夜，别突然发脾气，继续保持清淡饮食～");
    }

    // 3. 血糖建议：用「替换食物」简化选择
    if (data.fasting_glucose >= 6.1 || data.hba1c >= 5.7)
    { // 血糖异常
        sprintf(advice.blood_sugar,
                "血糖有点小波动，白米饭、白面包换成杂粮饭/全麦面包，水果选苹果、柚子这些不太甜的，每天饭后散散步，别坐着不动");
    }
    else
    { // 正常
        sprintf(advice.blood_sugar, "血糖很稳，继续少吃甜食，偶尔吃也没关系，记得动一动");
    }

    // 4. 血脂建议：用「具体食材」举例
    if (data.cholesterol >= 5.2 || data.ldl >= 3.4)
    { // 血脂异常
        sprintf(advice.blood_lipid,
                "血脂需要调整，少吃红烧肉、油炸食品，多吃三文鱼、核桃这些有好脂肪的，每周至少4次运动，比如骑自行车或打羽毛球");
    }
    else
    { // 正常
        sprintf(advice.blood_lipid, "血脂控制得好，继续少放油盐，偶尔吃顿好的也别过量");
    }

    // 5. 肾功能建议：用「日常习惯」提醒
    if (egfr < 90)
    { // 肾功能下降
        sprintf(advice.kidney,
                "肾要爱护好，少吃咸的和加工肉（比如火腿、香肠），吃药前看说明书有没有伤肾的，每天喝水1500-2000毫升，别憋尿");
    }
    else
    { // 正常
        sprintf(advice.kidney, "肾功能不错，少喝点酒，别乱吃药，多喝水别上火");
    }

    // 6. 吸烟建议：用「替代行为」帮助戒烟
    if (data.is_smoker)
    {
        sprintf(advice.smoking,
                "抽烟对身体不好哦，试试每天少抽1支，想抽的时候吃块口香糖或吃个小番茄，家人监督帮忙，慢慢把烟戒了");
    }
    else
    {
        sprintf(advice.smoking, "不抽烟很棒，别学别人抽，二手烟也躲着点");
    }

    // 7. 家族史建议：用「体检动作」明确方向
    if (data.family_history)
    {
        sprintf(advice.family,
                "家里有慢性病史，每年体检要做全，血糖、血压、血脂、肾功能都查一查，早发现早调理");
    }
    else
    {
        sprintf(advice.family, "家族史没问题，还是每年体检，防患于未然～");
    }

    // 8. 炎症建议：用「抗炎食物」引导饮食
    if (data.hs_crp >= 2.0)
    {
        sprintf(advice.inflammation,
                "身体有点发炎，多吃西兰花、蓝莓这些抗炎食物，少熬夜别太累，每天睡够7小时");
    }
    else
    {
        sprintf(advice.inflammation, "炎症指标还好，别吃太辣太油，保持规律作息");
    }

    // 9. 脂蛋白a建议：用「就医+情绪」双建议
    if (data.lp_a > 50)
    {
        sprintf(advice.lp_a,
                "脂蛋白a偏高，去医院问问医生需不需要吃药调理，平时少生气，压力别太大");
    }
    else
    {
        sprintf(advice.lp_a, "脂蛋白a正常，继续保持好心情，别让压力压垮自己");
    }

    // 10. 综合建议：拼接所有维度
    sprintf(advice.summary,
            "【体重】%s\n"
            "【血压】%s\n"
            "【血糖】%s\n"
            "【血脂】%s\n"
            "【肾功能】%s\n"
            "【吸烟】%s\n"
            "【家族史】%s\n"
            "【炎症】%s\n"
            "【脂蛋白a】%s\n"
            "坚持3个月后再测测指标，看看变化",
            advice.weight,
            advice.blood_pressure,
            advice.blood_sugar,
            advice.blood_lipid,
            advice.kidney,
            advice.smoking,
            advice.family,
            advice.inflammation,
            advice.lp_a);

    return advice;
}

// ========== 测试：生成并打印建议 ==========
int main()
{

    // 示例健康数据（可替换为真实用户数据）
    HealthData user = {
        .age = 52, .gender = 1, .height = 1.72, .weight = 80, .systolic_bp = 138, .diastolic_bp = 88, .fasting_glucose = 5.8, .hba1c = 5.7, .cholesterol = 5.2, .ldl = 3.3, .creatinine = 85, .is_smoker = 1, .family_history = 1, .lp_a = 45, .hs_crp = 1.8};

    HealthAdvice advice = generate_health_advice(user);
    printf("=============== 健康行动建议 ===============\n");
    printf("%s\n", advice.summary);

    return 0;
}