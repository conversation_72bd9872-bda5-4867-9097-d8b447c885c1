#include "driver/main.h"

// ????????
int importHealthData(HealthData *data, int maxEntries)
{
    FILE *file = fopen("function/txt/Data.txt", "r");
    if (file == NULL)
    {
        printf("Error: Cannot open file Data.txt\n");
        return 0;
    }

    int count = 0;
    while (fscanf(file, "%d %d %f %f %f %f %f %f %f %f %f %d %d %f %f",
                  &data[count].age, &data[count].gender, &data[count].height,
                  &data[count].weight, &data[count].systolic_bp,
                  &data[count].diastolic_bp, &data[count].fasting_glucose,
                  &data[count].hba1c, &data[count].cholesterol,
                  &data[count].ldl, &data[count].creatinine,
                  &data[count].is_smoker, &data[count].family_history,
                  &data[count].lp_a, &data[count].hs_crp) == 15)
    {
        count++;
        if (count >= maxEntries)
        {
            break;
        }
    }

    fclose(file);
    return count;
}