#include "function/driver/main.h"

int main()
{
    HealthData user[2];
    int count = importHealthData(user, 2);
    printf("Successfully imported %d records\n", count);

    for (int i = 0; i < count; i++)
    {
        printf("\n=== User %d Data ===\n", i + 1);
        printf("Age: %d, Gender: %d, Height: %.2f, Weight: %.2f\n",
               user[i].age, user[i].gender, user[i].height, user[i].weight);

        float bmi = user[i].weight / (user[i].height * user[i].height);
        printf("BMI: %.1f\n", bmi);

        printf("Blood Pressure: %.1f/%.1f\n", user[i].systolic_bp, user[i].diastolic_bp);
        printf("Glucose: %.1f, HbA1c: %.1f\n", user[i].fasting_glucose, user[i].hba1c);
        printf("Cholesterol: %.1f, LDL: %.1f\n", user[i].cholesterol, user[i].ldl);
        printf("Creatinine: %.1f, Smoker: %d, Family History: %d\n",
               user[i].creatinine, user[i].is_smoker, user[i].family_history);
        printf("Lp(a): %.1f, hs-CRP: %.1f\n", user[i].lp_a, user[i].hs_crp);

        HealthAdvice advice = generate_health_advice(user[i]);
        printf("=============== Health Action Advice ===============\n");
        printf("Weight: %s\n", advice.weight);
        printf("Blood Pressure: %s\n", advice.blood_pressure);
        printf("Blood Sugar: %s\n", advice.blood_sugar);
        printf("Blood Lipid: %s\n", advice.blood_lipid);
        printf("Kidney: %s\n", advice.kidney);
        printf("Smoking: %s\n", advice.smoking);
        printf("Family History: %s\n", advice.family);
        printf("Inflammation: %s\n", advice.inflammation);
        printf("Lp(a): %s\n", advice.lp_a);
    }

    return 0;
}